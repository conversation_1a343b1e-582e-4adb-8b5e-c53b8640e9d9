"""KiotViet API client implementation."""

import logging
from typing import Optional, Union
from datetime import datetime
from .base_client import BaseKiotVietClient
from ..config.settings import KiotVietConfig
from ...domain.entities.category import CategoryResponse
from ...domain.entities.invoice import InvoiceResponse

logger = logging.getLogger(__name__)


class KiotVietAPIClient(BaseKiotVietClient):
    """Client for interacting with KiotViet Public API.

    This class inherits all the common functionality (authentication, retry logic,
    error handling) from BaseKiotVietClient and only implements the specific
    API methods for KiotViet endpoints.
    """

    def __init__(self, config: KiotVietConfig):
        super().__init__(config)

    async def get_categories(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        hierarchical_data: bool = False
    ) -> CategoryResponse:
        """Get product categories from KiotViet API.

        Args:
            page_size: Number of items per page (max 100)
            current_item: Starting item index for pagination
            order_direction: Sort order ("Asc" or "Desc")
            hierarchical_data: Whether to return hierarchical structure

        Returns:
            Validated CategoryResponse with typed data
        """
        data = {
            "pageSize": min(page_size, 100),  # Ensure max limit
            "currentItem": current_item,
            "orderDirection": order_direction,
            "hierachicalData": hierarchical_data
        }

        raw_response = await self.make_request("GET", "/categories", data=data)
        return CategoryResponse.model_validate(raw_response)

    async def get_invoices_by_day(
        self,
        from_date: Union[str, datetime],
        to_date: Union[str, datetime],
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc"
    ) -> InvoiceResponse:
        """Get invoices for a specific date range from KiotViet API.

        Args:
            from_date: Start date (datetime object or ISO string)
            to_date: End date (datetime object or ISO string)
            page_size: Number of items per page (max 100)
            current_item: Starting item index for pagination
            order_direction: Sort order ("Asc" or "Desc")

        Returns:
            Validated InvoiceResponse with typed invoice data

        Raises:
            ValueError: If date format is invalid
            httpx.HTTPStatusError: For HTTP error responses
            httpx.RequestError: For network/connection errors
        """

        data = {
            "pageSize": min(page_size, 100),
            "currentItem": current_item,
            "orderDirection": order_direction,
            "fromPurchaseDate": from_date,
            "toPurchaseDate": to_date
        }

        raw_response = await self.make_request("GET", "/invoices", data=data)
        return InvoiceResponse.model_validate(raw_response)

