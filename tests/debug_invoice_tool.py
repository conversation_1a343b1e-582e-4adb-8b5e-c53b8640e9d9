#!/usr/bin/env python3
"""
Debug script for invoice tool - step by step debugging with breakpoints.
Perfect for debugging real API calls without pytest overhead.
"""

import asyncio
import sys
import traceback

async def debug_invoice_tool():
    """Debug invoice tool step by step - perfect for breakpoints!"""
    
    print("🐛 INVOICE TOOL DEBUG SESSION")
    print("=" * 60)
    print("💡 Set breakpoints anywhere in this function!")
    print("💡 Use your IDE debugger or add print() statements")
    print()
    
    # Step 1: Configuration
    print("📋 Step 1: Loading configuration...")
    try:
        from albatross_kiotviet_mcp.infrastructure.config.settings import get_config
        
        config = get_config()
        print(f"✅ Config loaded:")
        print(f"   Retailer: {config.retailer}")
        print(f"   API Base URL: {config.api_base_url}")
        print(f"   Client ID: {config.client_id[:8]}...")
        print(f"   Timeout: {config.request_timeout}s")
        print(f"   Max Retries: {config.max_retries}")
        
        # 🐛 BREAKPOINT: Set breakpoint here to inspect config
        breakpoint_1 = config  # You can inspect this in debugger
        
    except Exception as e:
        print(f"❌ Config failed: {e}")
        traceback.print_exc()
        return
    
    # Step 2: API Client
    print(f"\n🔌 Step 2: Creating API client...")
    try:
        from albatross_kiotviet_mcp.infrastructure.api.kiotviet_client import KiotVietAPIClient
        
        api_client = KiotVietAPIClient(config)
        print(f"✅ API client created: {type(api_client)}")
        
        # 🐛 BREAKPOINT: Set breakpoint here to inspect api_client
        breakpoint_2 = api_client
        
    except Exception as e:
        print(f"❌ API client creation failed: {e}")
        traceback.print_exc()
        return
    
    # Step 3: Invoices Tool
    print(f"\n🛠️ Step 3: Creating invoices tool...")
    try:
        from albatross_kiotviet_mcp.tools.invoices.invoices_tool import InvoicesTool
        
        invoices_tool = InvoicesTool(api_client)
        print(f"✅ Tool created:")
        print(f"   Name: {invoices_tool.name}")
        print(f"   Description: {invoices_tool.description[:50]}...")
        
        # 🐛 BREAKPOINT: Set breakpoint here to inspect tool
        breakpoint_3 = invoices_tool
        
    except Exception as e:
        print(f"❌ Tool creation failed: {e}")
        traceback.print_exc()
        return
    
    # Step 4: Test Parameters
    print(f"\n⚙️ Step 4: Setting up test parameters...")
    test_params = {
        "from_date": "2025-07-19",
        "to_date": "2025-07-19",
        "page_size": 3,  # Small for debugging
        "current_item": 0,
        "order_direction": "Asc"
    }
    print(f"✅ Test parameters:")
    for key, value in test_params.items():
        print(f"   {key}: {value}")
    
    # 🐛 BREAKPOINT: Set breakpoint here to modify parameters
    breakpoint_4 = test_params
    
    # Step 5: Parameter Validation
    print(f"\n🔍 Step 5: Validating parameters...")
    try:
        invoices_tool.pre_execute(**test_params)
        print(f"✅ Parameters validation passed")
        
    except Exception as e:
        print(f"❌ Parameter validation failed: {e}")
        traceback.print_exc()
        return
    
    # Step 6: Real API Call
    print(f"\n📡 Step 6: Making REAL API call...")
    try:
        print(f"   Calling: invoices_tool.execute(**{test_params})")
        
        # 🐛 BREAKPOINT: Set breakpoint here before API call
        breakpoint_5 = "About to make API call"
        
        result = await invoices_tool.execute(**test_params)
        
        print(f"🎉 API call successful!")
        print(f"   Result type: {type(result)}")
        print(f"   Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not dict'}")
        
        # 🐛 BREAKPOINT: Set breakpoint here to inspect result
        breakpoint_6 = result
        
    except Exception as e:
        print(f"❌ API call failed: {e}")
        print(f"   Error type: {type(e).__name__}")
        traceback.print_exc()
        
        # 🐛 BREAKPOINT: Set breakpoint here to debug errors
        breakpoint_error = e
        return
    
    # Step 7: Response Analysis
    print(f"\n🔍 Step 7: Analyzing response...")
    try:
        if isinstance(result, dict):
            print(f"📊 Response analysis:")
            print(f"   Total invoices: {result.get('total', 'N/A')}")
            print(f"   Page size: {result.get('pageSize', result.get('page_size', 'N/A'))}")
            print(f"   Data count: {len(result.get('data', []))}")
            
            # Analyze first invoice
            if result.get('data'):
                first_invoice = result['data'][0]
                print(f"\n📋 First invoice:")
                for key, value in first_invoice.items():
                    print(f"   {key}: {value}")
                
                # 🐛 BREAKPOINT: Set breakpoint here to inspect invoice data
                breakpoint_7 = first_invoice
        
        print(f"\n✅ Response analysis complete")
        
    except Exception as e:
        print(f"❌ Response analysis failed: {e}")
        traceback.print_exc()
    
    return result

def main():
    """Main debug function - run this for debugging!"""
    result = asyncio.run(debug_invoice_tool())

if __name__ == "__main__":
    main()