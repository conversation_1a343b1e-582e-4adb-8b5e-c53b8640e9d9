"""Date utility functions for KiotViet API integration.

This module provides shared date formatting and parsing utilities
that can be used across the project for consistent date handling.
"""

import logging
from typing import Union, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)


def format_date_to_iso(date_input: Union[str, datetime]) -> str:
    """Convert date input to ISO format string required by KiotViet API.
    
    This function handles multiple input formats and converts them to the
    standardized ISO format (YYYY-MM-DDTHH:mm:ss) expected by the KiotViet API.
    
    Args:
        date_input: Date as datetime object or string in various formats
        
    Returns:
        Date in ISO format (YYYY-MM-DDTHH:mm:ss)
        
    Raises:
        ValueError: If date format is invalid or cannot be parsed
        
    Examples:
        >>> format_date_to_iso("2025-07-19")
        '2025-07-19T00:00:00'
        
        >>> format_date_to_iso("2025-07-19T14:30:00")
        '2025-07-19T14:30:00'
        
        >>> from datetime import datetime
        >>> format_date_to_iso(datetime(2025, 7, 19, 14, 30))
        '2025-07-19T14:30:00'
    """
    if isinstance(date_input, datetime):
        return date_input.strftime("%Y-%m-%dT%H:%M:%S")
    elif isinstance(date_input, str):
        # Try to parse common date formats
        try:
            # Try ISO format first
            if 'T' in date_input:
                parsed_date = datetime.fromisoformat(date_input.replace('Z', '+00:00'))
                return parsed_date.strftime("%Y-%m-%dT%H:%M:%S")
            else:
                # Try date-only format (YYYY-MM-DD)
                parsed_date = datetime.strptime(date_input, "%Y-%m-%d")
                return parsed_date.strftime("%Y-%m-%dT00:00:00")
        except ValueError:
            try:
                # Try alternative formats
                for fmt in ["%Y-%m-%d %H:%M:%S", "%d/%m/%Y", "%m/%d/%Y"]:
                    try:
                        parsed_date = datetime.strptime(date_input, fmt)
                        return parsed_date.strftime("%Y-%m-%dT%H:%M:%S")
                    except ValueError:
                        continue
                raise ValueError(f"Unable to parse date format: {date_input}")
            except ValueError as e:
                raise ValueError(
                    f"Invalid date format: {date_input}. "
                    f"Expected formats: YYYY-MM-DD, YYYY-MM-DDTHH:mm:ss, or datetime object"
                ) from e
    else:
        raise ValueError(f"Date must be a string or datetime object, got {type(date_input)}")


def parse_kiotviet_date(date_string: str) -> datetime:
    """Parse a date string from KiotViet API response.
    
    KiotViet API typically returns dates in ISO format, but this function
    provides a centralized way to handle any variations in their date format.
    
    Args:
        date_string: Date string from KiotViet API response
        
    Returns:
        Parsed datetime object
        
    Raises:
        ValueError: If date string cannot be parsed
        
    Examples:
        >>> parse_kiotviet_date("2025-07-19T14:30:00")
        datetime.datetime(2025, 7, 19, 14, 30)
    """
    if not date_string:
        raise ValueError("Date string cannot be empty")
    
    try:
        # Handle ISO format with potential timezone info
        if 'T' in date_string:
            # Remove timezone info if present (Z or +00:00 format)
            clean_date = date_string.replace('Z', '').split('+')[0].split('-')[0:3]
            clean_date = '-'.join(clean_date[:3]) + 'T' + date_string.split('T')[1].split('+')[0].split('Z')[0]
            return datetime.fromisoformat(clean_date)
        else:
            # Handle date-only format
            return datetime.strptime(date_string, "%Y-%m-%d")
    except ValueError as e:
        raise ValueError(f"Unable to parse KiotViet date format: {date_string}") from e


def validate_date_range(from_date: Union[str, datetime], to_date: Union[str, datetime]) -> Tuple[datetime, datetime]:
    """Validate and parse a date range.
    
    This function ensures that the date range is valid (from_date <= to_date)
    and returns parsed datetime objects for both dates.
    
    Args:
        from_date: Start date (string or datetime)
        to_date: End date (string or datetime)
        
    Returns:
        Tuple of (from_datetime, to_datetime)
        
    Raises:
        ValueError: If dates are invalid or from_date > to_date
        
    Examples:
        >>> from_dt, to_dt = validate_date_range("2025-07-19", "2025-07-20")
        >>> from_dt < to_dt
        True
    """
    # Convert to datetime objects for comparison
    if isinstance(from_date, str):
        from_dt = datetime.fromisoformat(format_date_to_iso(from_date))
    else:
        from_dt = from_date
        
    if isinstance(to_date, str):
        to_dt = datetime.fromisoformat(format_date_to_iso(to_date))
    else:
        to_dt = to_date
    
    # Validate range
    if from_dt > to_dt:
        raise ValueError(f"from_date ({from_dt}) cannot be after to_date ({to_dt})")
    
    return from_dt, to_dt


def get_date_range_days(from_date: Union[str, datetime], to_date: Union[str, datetime]) -> int:
    """Calculate the number of days in a date range.
    
    Args:
        from_date: Start date (string or datetime)
        to_date: End date (string or datetime)
        
    Returns:
        Number of days in the range (inclusive)
        
    Examples:
        >>> get_date_range_days("2025-07-19", "2025-07-19")
        1
        >>> get_date_range_days("2025-07-19", "2025-07-21")
        3
    """
    from_dt, to_dt = validate_date_range(from_date, to_date)
    return (to_dt - from_dt).days + 1
