# KiotViet MCP Server

A professional Model Context Protocol (MCP) server for integrating with KiotViet Public API. This server enables AI assistants like <PERSON> to interact with KiotViet's retail management system through natural language queries.

## Features

- **🏗️ Clean Architecture**: Domain-driven design with clear separation of concerns
- **🔐 Secure Authentication**: Automatic OAuth2 token management with refresh handling
- **🛠️ MCP Tools**: Categories, products, and branches retrieval with pagination
- **🔒 Type Safety**: Complete type hints with Pydantic validation
- **🧪 Comprehensive Testing**: Unit and integration tests with real API validation
- **⚡ Production Ready**: Error handling, logging, and async/await throughout
- **🔧 Extensible**: Easy-to-follow patterns for adding new tools

## Project Structure

This project follows **Clean Architecture** principles with clear separation of concerns:

```
src/albatross_kiotviet_mcp/
├── domain/                        # Business logic layer
│   ├── entities/                  # Domain entities (Category, Product, Branch)
│   │   ├── category.py           # Category entity with Pydantic validation
│   └── interfaces/               # Abstract interfaces
│       └── api_client.py         # IKiotVietAPIClient interface
├── infrastructure/               # External concerns layer
│   ├── api/                      # API client implementations
│   │   ├── base_client.py        # Base HTTP client with auth
│   │   └── kiotviet_client.py    # KiotViet-specific API client
│   ├── auth/                     # Authentication management
│   │   └── token_manager.py     # OAuth2 token lifecycle
│   └── config/                   # Configuration management
│       └── settings.py           # Pydantic settings with env vars
├── tools/                        # MCP tools layer
│   ├── base/                     # Base tool classes
│   │   └── base_tool.py          # BaseMCPTool abstract class
│   └── categories/               # Categories tool implementation
│       └── categories_tool.py    # CategoriesTool with validation
├── application/                  # Application services layer
│   └── services/                 # Business services (future)
├── main.py                       # Application entry point
└── server.py                     # FastMCP server setup and tool registration
```

### Architecture Benefits

- **Domain Layer**: Pure business logic, no external dependencies
- **Infrastructure Layer**: Handles external APIs, configuration, authentication
- **Tools Layer**: MCP-specific implementations with clean interfaces
- **Application Layer**: Orchestrates business workflows (extensible)

## Getting Started

### Prerequisites

- **Python 3.10+**
- **KiotViet API Credentials**: Client ID, Client Secret, Retailer name
- **uv** (recommended) or pip for dependency management

### 1. Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd albatross-kiotviet-mcp

# Install dependencies with uv (recommended)
uv sync

# Or with pip
pip install -e .
```

### 2. Configuration

Create a `.env` file with your KiotViet credentials:

```env
KIOTVIET_CLIENT_ID=your_client_id_here
KIOTVIET_CLIENT_SECRET=your_client_secret_here
KIOTVIET_RETAILER=your_retailer_name_here
```

### 3. Run the MCP Server

```bash
# Using uv (recommended)
uv run albatross-kiotviet-mcp-server

# Or using the installed script
albatross-kiotviet-mcp-server

# Development mode with auto-reload
uv run python -m albatross_kiotviet_mcp.main
```

### 4. Connect from Claude Desktop

Edit your Claude Desktop configuration file:

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "kiotviet": {
      "command": "uv",
      "args": [
        "--directory",
        "/path/to/albatross-kiotviet-mcp",
        "run",
        "albatross-kiotviet-mcp-server"
      ],
      "env": {
        "PYTHONUNBUFFERED": "1"
      }
    }
  }
}
```

Restart Claude Desktop to load the configuration.

## Available Tools

The server currently provides **3 MCP tools** for interacting with KiotViet:

### `get_categories`
Retrieve product categories with pagination and hierarchical support.

**Parameters:**
- `page_size` (int, optional): Items per page (1-100, default: 50)
- `current_item` (int, optional): Starting index (default: 0)
- `order_direction` (str, optional): "Asc" or "Desc" (default: "Asc")
- `hierarchical_data` (bool, optional): Return hierarchical structure (default: False)

**Example Usage in Claude:**
```
Get all product categories from KiotViet
Show me categories in descending order with hierarchical structure
```

### `get_invoices_by_day`
Retrieve invoices for a specific date range with pagination support.

**Parameters:**
- `from_date` (str|datetime, required): Start date (e.g., "2025-07-19" or datetime object)
- `to_date` (str|datetime, required): End date (e.g., "2025-07-19" or datetime object)
- `page_size` (int, optional): Items per page (1-100, default: 50)
- `current_item` (int, optional): Starting index (default: 0)
- `order_direction` (str, optional): "Asc" or "Desc" (default: "Asc")

**Example Usage in Claude:**
```
Get invoices for July 19, 2025
Show me all invoices from July 1 to July 31, 2025
Get invoices for today with pagination, show 10 items starting from item 20
```

### `calculate_daily_revenue`
Calculate revenue metrics for a specific date range from KiotViet invoices.

**Parameters:**
- `from_date` (str|datetime, required): Start date (e.g., "2025-07-19" or datetime object)
- `to_date` (str|datetime, required): End date (e.g., "2025-07-19" or datetime object)
- `page_size` (int, optional): Items per page for pagination (1-100, default: 100)
- `include_details` (bool, optional): Include detailed invoice breakdown (default: False)

**Returns:**
- `date_range`: The queried date range
- `invoice_count`: Total number of invoices processed
- `gross_revenue`: Sum of all invoice totals
- `net_revenue`: Sum of all payments received
- `payment_difference`: Difference between gross and net revenue
- `average_invoice_value`: Average value per invoice
- `total_discount`: Sum of all discounts applied

**Example Usage in Claude:**
```
Calculate revenue for July 19, 2025
Show me revenue metrics from July 1 to July 31, 2025 with detailed breakdown
Calculate daily revenue for this week with invoice details
```