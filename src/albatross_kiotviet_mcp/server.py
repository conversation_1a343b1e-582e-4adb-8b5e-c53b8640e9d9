"""KiotViet MCP Server."""

import logging
from typing import Optional
from fastmcp import FastMCP

from .infrastructure.config.settings import get_config
from .infrastructure.api.kiotviet_client import KiotVietAPIClient
from .tools.categories.categories_tool import CategoriesTool
from .tools.invoices.invoices_tool import InvoicesTool
from .tools.revenue.revenue_tool import RevenueTool

logger = logging.getLogger(__name__)

# Initialize FastMCP server
server = FastMCP(name="KiotVietMCPServer")

# Global instances (lazy-loaded)
_kiotviet_client: Optional[KiotVietAPIClient] = None
_config = None
_categories_tool: Optional[CategoriesTool] = None
_invoices_tool: Optional[InvoicesTool] = None
_revenue_tool: Optional[RevenueTool] = None


def get_kiotviet_client() -> KiotVietAPIClient:
    """Get or create KiotViet API client instance."""
    global _kiotviet_client, _config

    if _kiotviet_client is None:
        _config = get_config()
        _kiotviet_client = KiotVietAPIClient(_config)

    return _kiotviet_client


def get_categories_tool() -> CategoriesTool:
    """Get or create categories tool instance."""
    global _categories_tool

    if _categories_tool is None:
        client = get_kiotviet_client()
        _categories_tool = CategoriesTool(client)

    return _categories_tool


def get_invoices_tool() -> InvoicesTool:
    """Get or create invoices tool instance."""
    global _invoices_tool

    if _invoices_tool is None:
        client = get_kiotviet_client()
        _invoices_tool = InvoicesTool(client)

    return _invoices_tool


def get_revenue_tool() -> RevenueTool:
    """Get or create revenue tool instance."""
    global _revenue_tool

    if _revenue_tool is None:
        client = get_kiotviet_client()
        _revenue_tool = RevenueTool(client)

    return _revenue_tool


# MCP Tool Registration

def register_tools():
    """Register all MCP tools with the FastMCP server."""
    logger.info("Registering MCP tools...")

    # Get tool instances
    categories_tool = get_categories_tool()
    invoices_tool = get_invoices_tool()
    revenue_tool = get_revenue_tool()

    # Register categories tool
    server.tool(
        name=categories_tool.name,
        description=categories_tool.description
    )(categories_tool.get_wrapper_function())

    # Register invoices tool
    server.tool(
        name=invoices_tool.name,
        description=invoices_tool.description
    )(invoices_tool.get_wrapper_function())

    # Register revenue tool
    server.tool(
        name=revenue_tool.name,
        description=revenue_tool.description
    )(revenue_tool.get_wrapper_function())

    logger.info("Successfully registered all MCP tools")


def run():
    """Entry point for running the FastMCP server."""
    logger.info(f"Starting {server.name}...")

    # Validate configuration on startup
    try:
        config = get_config()
        logger.info(f"Configuration loaded successfully for retailer: {config.retailer}")
    except Exception as e:
        logger.error(f"Configuration error: {e}")
        raise

    # Register all tools
    register_tools()

    return server.run()