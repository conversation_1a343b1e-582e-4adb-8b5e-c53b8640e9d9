"""Invoice domain entities."""

from typing import Optional, List, Union
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict


class InvoiceLineItem(BaseModel):
    """KiotViet invoice line item entity."""
    
    id: Optional[int] = Field(default=None, description="Line item ID")
    productId: Optional[int] = Field(default=None, description="Product ID")
    productCode: Optional[str] = Field(default=None, description="Product code")
    productName: Optional[str] = Field(default=None, description="Product name")
    quantity: Optional[float] = Field(default=None, description="Quantity")
    price: Optional[float] = Field(default=None, description="Unit price")
    discount: Optional[float] = Field(default=None, description="Discount amount")
    discountRatio: Optional[float] = Field(default=None, description="Discount ratio")
    subTotal: Optional[float] = Field(default=None, description="Subtotal amount")
    
    model_config = ConfigDict(from_attributes=True)

class Invoice(BaseModel):
    """KiotViet invoice entity."""
    
    id: Optional[int] = Field(default=None, description="Invoice ID")
    code: Optional[str] = Field(default=None, description="Invoice code")
    purchaseDate: Optional[str] = Field(default=None, description="Purchase date in ISO format")
    branchId: Optional[int] = Field(default=None, description="Branch ID")
    branchName: Optional[str] = Field(default=None, description="Branch name")
    customerId: Optional[int] = Field(default=None, description="Customer ID")
    customerCode: Optional[str] = Field(default=None, description="Customer code")
    customerName: Optional[str] = Field(default=None, description="Customer name")
    total: Optional[float] = Field(default=None, description="Total amount")
    totalPayment: Optional[float] = Field(default=None, description="Total payment amount")
    status: Optional[int] = Field(default=None, description="Invoice status")
    discount: Optional[float] = Field(default=None, description="Total discount")
    description: Optional[str] = Field(default=None, description="Invoice description")
    soldById: Optional[int] = Field(default=None, description="Sold by user ID")
    soldByName: Optional[str] = Field(default=None, description="Sold by user name")
    createdDate: Optional[str] = Field(default=None, description="Created date")
    modifiedDate: Optional[str] = Field(default=None, description="Modified date")
    
    # Nested objects
    invoiceDetails: Optional[List[InvoiceLineItem]] = Field(default=None, description="Invoice line items")
    
    model_config = ConfigDict(from_attributes=True)


class InvoiceResponse(BaseModel):
    """Response model for invoice API calls."""
    
    total: int = Field(description="Total number of invoices")
    pageSize: int = Field(description="Number of items per page")
    data: List[Invoice] = Field(description="List of invoices")
    
    model_config = ConfigDict(from_attributes=True)
