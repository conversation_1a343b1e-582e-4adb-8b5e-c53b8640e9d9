#!/usr/bin/env python3
"""Debug script for revenue calculation tool.

This script provides step-by-step debugging for the revenue tool
with real API calls and detailed logging.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from albatross_kiotviet_mcp.infrastructure.config.settings import get_config
from albatross_kiotviet_mcp.infrastructure.api.kiotviet_client import KiotVietAPIClient
from albatross_kiotviet_mcp.tools.revenue.revenue_tool import RevenueTool

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def debug_revenue_tool():
    """Debug the revenue calculation tool step by step."""
    
    print("🚀 Starting Revenue Tool Debug Session")
    print("=" * 60)
    
    # Step 1: Configuration
    print(f"\n📋 Step 1: Loading configuration...")
    try:
        config = get_config()
        print(f"✅ Configuration loaded:")
        print(f"   Retailer: {config.retailer}")
        print(f"   API Base URL: {config.api_base_url}")
        print(f"   Request Timeout: {config.request_timeout}s")
        
        # 🐛 BREAKPOINT: Set breakpoint here to inspect config
        breakpoint_1 = config
        
    except Exception as e:
        print(f"❌ Configuration failed: {e}")
        print("💡 Make sure your .env file is configured with KiotViet credentials")
        return
    
    # Step 2: API Client
    print(f"\n🔌 Step 2: Creating API client...")
    try:
        api_client = KiotVietAPIClient(config)
        print(f"✅ API client created successfully")
        
        # 🐛 BREAKPOINT: Set breakpoint here to inspect client
        breakpoint_2 = api_client
        
    except Exception as e:
        print(f"❌ API client creation failed: {e}")
        return
    
    # Step 3: Revenue Tool
    print(f"\n🛠️ Step 3: Creating revenue tool...")
    try:
        revenue_tool = RevenueTool(api_client)
        print(f"✅ Tool created:")
        print(f"   Name: {revenue_tool.name}")
        print(f"   Description: {revenue_tool.description[:50]}...")
        
        # 🐛 BREAKPOINT: Set breakpoint here to inspect tool
        breakpoint_3 = revenue_tool
        
    except Exception as e:
        print(f"❌ Tool creation failed: {e}")
        return
    
    # Step 4: Parameter Validation
    print(f"\n✅ Step 4: Testing parameter validation...")
    try:
        # Test valid parameters
        revenue_tool.pre_execute(
            from_date="2025-07-19",
            to_date="2025-07-19",
            page_size=50,
            include_details=False
        )
        print(f"✅ Parameter validation passed")
        
        # Test invalid parameters
        try:
            revenue_tool.pre_execute(
                from_date="",
                to_date="2025-07-19"
            )
            print(f"❌ Validation should have failed for empty from_date")
        except ValueError as e:
            print(f"✅ Validation correctly caught error: {e}")
        
    except Exception as e:
        print(f"❌ Parameter validation failed: {e}")
        return
    
    # Step 5: Revenue Calculation (Basic)
    print(f"\n💰 Step 5: Testing basic revenue calculation...")
    try:
        result = await revenue_tool.execute(
            from_date="2025-07-19",
            to_date="2025-07-19",
            page_size=10  # Small page size for testing
        )
        
        print(f"✅ Revenue calculation completed:")
        print(f"   Date Range: {result['date_range']}")
        print(f"   Invoice Count: {result['invoice_count']}")
        print(f"   Gross Revenue: {result['gross_revenue']:,.2f}")
        print(f"   Net Revenue: {result['net_revenue']:,.2f}")
        print(f"   Payment Difference: {result['payment_difference']:,.2f}")
        print(f"   Average Invoice Value: {result['average_invoice_value']:,.2f}")
        print(f"   Total Discount: {result['total_discount']:,.2f}")
        
        # 🐛 BREAKPOINT: Set breakpoint here to inspect result
        breakpoint_5 = result
        
    except Exception as e:
        print(f"❌ Revenue calculation failed: {e}")
        print(f"💡 This might be due to:")
        print(f"   - Invalid API credentials")
        print(f"   - Network connectivity issues")
        print(f"   - No invoices found for the specified date")
        return
    
    # Step 6: Revenue Calculation (With Details)
    print(f"\n📊 Step 6: Testing detailed revenue calculation...")
    try:
        detailed_result = await revenue_tool.execute(
            from_date="2025-07-19",
            to_date="2025-07-20",  # 2-day range
            page_size=10,
            include_details=True
        )
        
        print(f"✅ Detailed revenue calculation completed:")
        print(f"   Date Range: {detailed_result['date_range']}")
        print(f"   Invoice Count: {detailed_result['invoice_count']}")
        print(f"   Gross Revenue: {detailed_result['gross_revenue']:,.2f}")
        
        # Show invoice details if available
        if 'invoice_details' in detailed_result and detailed_result['invoice_details']:
            print(f"\n📋 Invoice Details (first 3):")
            for i, invoice in enumerate(detailed_result['invoice_details'][:3]):
                print(f"   {i+1}. {invoice['code']}: {invoice['total']:,.2f} "
                      f"({invoice['branch_name']})")
        
        # Show daily breakdown if available
        if 'daily_breakdown' in detailed_result and detailed_result['daily_breakdown']:
            print(f"\n📅 Daily Breakdown:")
            for date, data in detailed_result['daily_breakdown'].items():
                print(f"   {date}: {data['invoice_count']} invoices, "
                      f"{data['gross_revenue']:,.2f} revenue")
        
        # 🐛 BREAKPOINT: Set breakpoint here to inspect detailed result
        breakpoint_6 = detailed_result
        
    except Exception as e:
        print(f"❌ Detailed revenue calculation failed: {e}")
        return
    
    # Step 7: Wrapper Function Test
    print(f"\n🔧 Step 7: Testing wrapper function...")
    try:
        wrapper_func = revenue_tool.get_wrapper_function()
        print(f"✅ Wrapper function created:")
        print(f"   Function name: {wrapper_func.__name__}")
        print(f"   Function callable: {callable(wrapper_func)}")
        
        # Test wrapper function call
        wrapper_result = await wrapper_func(
            from_date="2025-07-19",
            to_date="2025-07-19"
        )
        
        print(f"✅ Wrapper function call successful:")
        print(f"   Result type: {type(wrapper_result)}")
        print(f"   Invoice count: {wrapper_result.get('invoice_count', 'N/A')}")
        
        # 🐛 BREAKPOINT: Set breakpoint here to inspect wrapper result
        breakpoint_7 = wrapper_result
        
    except Exception as e:
        print(f"❌ Wrapper function test failed: {e}")
        return
    
    print(f"\n🎉 All tests completed successfully!")
    print("=" * 60)
    print("💡 Revenue tool is ready for production use!")


def main():
    """Main function to run the debug session."""
    print("KiotViet Revenue Tool Debug Session")
    print("Make sure your .env file is configured before running this script")
    print()
    
    try:
        asyncio.run(debug_revenue_tool())
    except KeyboardInterrupt:
        print("\n🛑 Debug session interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
